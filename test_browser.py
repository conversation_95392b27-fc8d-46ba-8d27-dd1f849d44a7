#!/usr/bin/env python3
"""
Test script để kiểm tra browser fallback mechanism
"""

import sys
import os
from playwright.sync_api import sync_playwright

def test_browser_fallback():
    """Test browser fallback mechanism"""
    try:
        print("Đang khởi động Playwright...")
        
        with sync_playwright() as p:
            print("Playwright đã khởi động thành công")
            
            # Test Firefox trước
            try:
                print("Đang test Firefox...")
                browser = p.firefox.launch(headless=True)
                context = browser.new_context()
                page = context.new_page()
                page.goto("https://www.google.com")
                title = page.title()
                print(f"✅ Firefox hoạt động! Tiêu đề: {title}")
                browser.close()
                return "firefox"
            except Exception as e:
                print(f"❌ Firefox không hoạt động: {e}")
                
                # Fallback về Chromium
                try:
                    print("Đang fallback về Chromium...")
                    browser = p.chromium.launch(headless=True)
                    context = browser.new_context()
                    page = context.new_page()
                    page.goto("https://www.google.com")
                    title = page.title()
                    print(f"✅ Chromium hoạt động! Tiêu đề: {title}")
                    browser.close()
                    return "chromium"
                except Exception as e2:
                    print(f"❌ Chromium cũng không hoạt động: {e2}")
                    return None
            
    except Exception as e:
        print(f"❌ Lỗi tổng quát: {e}")
        return None

if __name__ == "__main__":
    result = test_browser_fallback()
    if result:
        print(f"🎉 Browser {result} sẵn sàng sử dụng!")
        sys.exit(0)
    else:
        print("💥 Không có browser nào hoạt động!")
        sys.exit(1)
