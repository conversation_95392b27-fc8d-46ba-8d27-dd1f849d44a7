@echo off
echo ========================================
echo Building Data Assortment All In One
echo ========================================

echo.
echo Step 1: Checking dependencies...
python check_dependencies.py
if errorlevel 1 (
    echo.
    echo Dependencies check failed! Please install missing dependencies.
    pause
    exit /b 1
)

echo.
echo Step 2: Installing/Updating dependencies...
pip install -r requirements.txt

echo.
echo Step 2: Installing Playwright browsers...
playwright install chromium

echo.
echo Step 3: Cleaning previous build...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"

echo.
echo Step 4: Building executable with PyInstaller...
pyinstaller main.spec

echo.
echo Step 5: Checking build result...
if exist "dist\DataAssortmentAIO.exe" (
    echo ========================================
    echo BUILD SUCCESSFUL!
    echo ========================================
    echo Executable created: dist\DataAssortmentAIO.exe
    echo.
    echo Note: Make sure the following files are in the same directory as the exe:
    echo - UI.ico
    echo - Splash.gif
    echo - Splash.jpg
    echo.
    echo You can also copy these files to the dist folder if needed.
) else (
    echo ========================================
    echo BUILD FAILED!
    echo ========================================
    echo Please check the error messages above.
)

echo.
pause
