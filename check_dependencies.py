#!/usr/bin/env python3
"""
Script để kiểm tra tất cả dependencies trước khi build
"""

import os
import sys
import importlib
import subprocess

def check_module(module_name, package_name=None):
    """Kiểm tra xem module có thể import đ<PERSON><PERSON><PERSON> không"""
    try:
        importlib.import_module(module_name)
        print(f"✓ {module_name} - OK")
        return True
    except ImportError as e:
        print(f"✗ {module_name} - MISSING")
        if package_name:
            print(f"  Install with: pip install {package_name}")
        else:
            print(f"  Install with: pip install {module_name}")
        return False

def check_file(file_path):
    """Kiểm tra xem file có tồn tại không"""
    if os.path.exists(file_path):
        print(f"✓ {file_path} - OK")
        return True
    else:
        print(f"✗ {file_path} - MISSING")
        return False

def check_playwright_browsers():
    """Kiểm tra Playwright browsers"""
    try:
        result = subprocess.run(['playwright', 'list'], 
                              capture_output=True, text=True, timeout=10)
        if 'chromium' in result.stdout.lower():
            print("✓ Playwright Chromium browser - OK")
            return True
        else:
            print("✗ Playwright Chromium browser - MISSING")
            print("  Install with: playwright install chromium")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("✗ Playwright CLI - MISSING")
        print("  Install with: pip install playwright && playwright install")
        return False

def main():
    print("=" * 50)
    print("KIỂM TRA DEPENDENCIES CHO DATA ASSORTMENT AIO")
    print("=" * 50)
    
    all_ok = True
    
    print("\n1. Kiểm tra Python modules:")
    print("-" * 30)
    
    # Core modules
    modules_to_check = [
        ('PyQt6.QtWidgets', 'PyQt6'),
        ('PyQt6.QtCore', 'PyQt6'),
        ('PyQt6.QtGui', 'PyQt6'),
        ('playwright.sync_api', 'playwright'),
        ('playwright_stealth', 'playwright-stealth'),
        ('gspread', 'gspread'),
        ('google.oauth2.service_account', 'google-auth'),
        ('google_auth_oauthlib.flow', 'google-auth-oauthlib'),
        ('pandas', 'pandas'),
        ('openpyxl', 'openpyxl'),
        ('selenium', 'selenium'),
        ('requests', 'requests'),
        ('openai', 'openai'),
    ]
    
    for module, package in modules_to_check:
        if not check_module(module, package):
            all_ok = False
    
    print("\n2. Kiểm tra custom modules:")
    print("-" * 30)
    
    custom_modules = [
        'data_scrape_ui',
        'import_data',
        'internal_data',
        'raw_data_ui',
        'image_scraping',
        'external_update',
        'ai_classification',
        'basket_package',
        'gsheet_manager',
        'error_handler',
        'data_scrape_core',
        'raw_data_core',
        'thread_pool_fix',
    ]
    
    for module in custom_modules:
        if not check_module(module):
            all_ok = False
    
    print("\n3. Kiểm tra resource files:")
    print("-" * 30)
    
    resource_files = [
        'UI.ico',
        'Splash.gif',
        'Splash.jpg',
        'main.py',
        'main.spec',
        'requirements.txt'
    ]
    
    for file_path in resource_files:
        if not check_file(file_path):
            all_ok = False
    
    print("\n4. Kiểm tra Playwright browsers:")
    print("-" * 30)
    
    if not check_playwright_browsers():
        all_ok = False
    
    print("\n" + "=" * 50)
    if all_ok:
        print("✓ TẤT CẢ DEPENDENCIES ĐÃ SẴN SÀNG!")
        print("Bạn có thể chạy build.bat để đóng gói ứng dụng.")
    else:
        print("✗ CÓ VẤN ĐỀ VỚI DEPENDENCIES!")
        print("Vui lòng cài đặt các module/file bị thiếu trước khi build.")
    print("=" * 50)
    
    return all_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
