# -*- mode: python ; coding: utf-8 -*-

import os
import sys
from pathlib import Path

# Get the current directory
current_dir = os.path.dirname(os.path.abspath(SPEC))

# Define all Python modules that need to be included
hiddenimports = [
    'data_scrape_ui',
    'import_data',
    'internal_data',
    'raw_data_ui',
    'image_scraping',
    'external_update',
    'ai_classification',
    'basket_package',
    'gsheet_manager',
    'error_handler',
    'data_scrape_core',
    'raw_data_core',
    'thread_pool_fix',
    'playwright',
    'playwright.sync_api',
    'playwright_stealth',
    'gspread',
    'google.oauth2.service_account',
    'google_auth_oauthlib.flow',
    'google.auth.transport.requests',
    'google.auth.exceptions',
    'PyQt6.QtWidgets',
    'PyQt6.QtCore',
    'PyQt6.QtGui',
    'openpyxl',
    'pandas',
    'selenium',
    'requests',
    'openai',
    'sqlite3',
    'pickle',
    'json',
    'base64',
    'tempfile',
    'pathlib',
    'datetime',
    'threading',
    'concurrent.futures',
    'queue',
    'time',
    'random',
    'string',
    'shutil',
    'unicodedata',
    'difflib',
    'collections',
    'hashlib',
    'traceback',
    're'
]

# Define data files that need to be included
datas = [
    ('UI.ico', '.'),
    ('Splash.gif', '.'),
    ('Splash.jpg', '.'),
]

# Check if files exist and add them if they do
resource_files = ['UI.ico', 'Splash.gif', 'Splash.jpg']
existing_datas = []

for file in resource_files:
    file_path = os.path.join(current_dir, file)
    if os.path.exists(file_path):
        existing_datas.append((file_path, '.'))
        print(f"Found resource file: {file}")
    else:
        print(f"Warning: Resource file not found: {file}")

# Define binaries (if any)
binaries = []

# Analysis
a = Analysis(
    ['main.py'],
    pathex=[current_dir],
    binaries=binaries,
    datas=existing_datas,
    hiddenimports=hiddenimports,
    hookspath=[current_dir],  # Add current directory to hookspath
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# Remove duplicate entries
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# Create executable
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='DataAssortmentAIO',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # Set to False to hide console window
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='UI.ico' if os.path.exists(os.path.join(current_dir, 'UI.ico')) else None,
)
